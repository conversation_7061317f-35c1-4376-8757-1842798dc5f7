<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Personal Finance Manager - Complete Financial Dashboard</title>
    <link rel="stylesheet" href="styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  </head>
  <body>
    <!-- Navigation Sidebar -->
    <nav class="sidebar" id="sidebar">
      <div class="sidebar-header">
        <h2>💰 Finance Manager</h2>
        <button class="sidebar-toggle" id="sidebarToggle">
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>

      <ul class="nav-menu">
        <li class="nav-item">
          <a href="#overview" class="nav-link active" data-page="overview">
            <span class="nav-icon">📊</span>
            <span class="nav-text">Overview</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#income" class="nav-link" data-page="income">
            <span class="nav-icon">💵</span>
            <span class="nav-text">Income</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#expenses" class="nav-link" data-page="expenses">
            <span class="nav-icon">💸</span>
            <span class="nav-text">Expenses</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#investments" class="nav-link" data-page="investments">
            <span class="nav-icon">📈</span>
            <span class="nav-text">Investments</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#budget" class="nav-link" data-page="budget">
            <span class="nav-icon">🎯</span>
            <span class="nav-text">Budget</span>
          </a>
        </li>
        <li class="nav-item">
          <a href="#credit-import" class="nav-link" data-page="credit-import">
            <span class="nav-icon">💳</span>
            <span class="nav-text">Credit Card Import</span>
          </a>
        </li>
      </ul>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
      <!-- Overview Dashboard Page -->
      <div class="page active" id="overview-page">
        <div class="page-header">
          <h1>Financial Overview</h1>
          <p>Your complete financial snapshot at a glance</p>
        </div>

        <!-- Key Metrics Cards -->
        <div class="metrics-grid">
          <div class="metric-card balance">
            <div class="metric-icon">💰</div>
            <div class="metric-content">
              <h3>Net Worth</h3>
              <p class="metric-value" id="netWorth">₹0.00</p>
              <span class="metric-change positive" id="netWorthChange"
                >+0.00%</span
              >
            </div>
          </div>

          <div class="metric-card income">
            <div class="metric-icon">💵</div>
            <div class="metric-content">
              <h3>Monthly Income</h3>
              <p class="metric-value" id="monthlyIncome">₹0.00</p>
              <span class="metric-change positive" id="incomeChange"
                >+0.00%</span
              >
            </div>
          </div>

          <div class="metric-card expense">
            <div class="metric-icon">💸</div>
            <div class="metric-content">
              <h3>Monthly Expenses</h3>
              <p class="metric-value" id="monthlyExpenses">₹0.00</p>
              <span class="metric-change negative" id="expenseChange"
                >+0.00%</span
              >
            </div>
          </div>

          <div class="metric-card investment">
            <div class="metric-icon">📈</div>
            <div class="metric-content">
              <h3>Investments</h3>
              <p class="metric-value" id="totalInvestments">₹0.00</p>
              <span class="metric-change positive" id="investmentChange"
                >+0.00%</span
              >
            </div>
          </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-grid">
          <div class="chart-card">
            <h3>Spending by Category</h3>
            <canvas id="expenseChart"></canvas>
          </div>

          <div class="chart-card">
            <h3>Income vs Expenses</h3>
            <canvas id="incomeExpenseChart"></canvas>
          </div>

          <div class="chart-card">
            <h3>Net Worth Trend</h3>
            <canvas id="netWorthChart"></canvas>
          </div>

          <div class="chart-card">
            <h3>Investment Portfolio</h3>
            <canvas id="portfolioChart"></canvas>
          </div>
        </div>

        <!-- Recent Transactions -->
        <div class="recent-transactions">
          <h3>Recent Transactions</h3>
          <div class="transaction-list" id="recentTransactions">
            <!-- Recent transactions will be populated here -->
          </div>
        </div>
      </div>

      <!-- Income Management Page -->
      <div class="page" id="income-page">
        <div class="page-header">
          <h1>Income Management</h1>
          <p>Track and manage all your income sources</p>
        </div>

        <!-- Income Summary Cards -->
        <div class="income-summary">
          <div class="summary-card">
            <h4>This Month</h4>
            <p class="amount" id="currentMonthIncome">₹0.00</p>
          </div>
          <div class="summary-card">
            <h4>Last Month</h4>
            <p class="amount" id="lastMonthIncome">₹0.00</p>
          </div>
          <div class="summary-card">
            <h4>Average Monthly</h4>
            <p class="amount" id="avgMonthlyIncome">₹0.00</p>
          </div>
        </div>

        <!-- Add Income Form -->
        <div class="form-section">
          <h3>Add Income Entry</h3>
          <form id="incomeForm" class="finance-form">
            <div class="form-row">
              <div class="form-control">
                <label for="incomeSource">Income Source</label>
                <input
                  type="text"
                  id="incomeSource"
                  placeholder="e.g., Salary, Freelance"
                  required
                />
              </div>
              <div class="form-control">
                <label for="incomeAmount">Amount (₹)</label>
                <input
                  type="number"
                  id="incomeAmount"
                  placeholder="Enter amount"
                  step="0.01"
                  min="0"
                  required
                />
              </div>
            </div>
            <div class="form-row">
              <div class="form-control">
                <label for="incomeCategory">Category</label>
                <select id="incomeCategory" required>
                  <option value="">Select Category</option>
                  <option value="Salary">Salary</option>
                  <option value="Freelance">Freelance</option>
                  <option value="Business">Business</option>
                  <option value="Investment Returns">Investment Returns</option>
                  <option value="Rental">Rental Income</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div class="form-control">
                <label for="incomeDate">Date</label>
                <input type="date" id="incomeDate" required />
              </div>
            </div>
            <div class="form-control">
              <label for="incomeRecurring">
                <input type="checkbox" id="incomeRecurring" />
                Recurring Income
              </label>
            </div>
            <button type="submit" class="btn primary">Add Income</button>
          </form>
        </div>

        <!-- Income History -->
        <div class="data-section">
          <div class="section-header">
            <h3>Income History</h3>
            <div class="filter-controls">
              <select id="incomeFilter">
                <option value="all">All Categories</option>
                <option value="Salary">Salary</option>
                <option value="Freelance">Freelance</option>
                <option value="Business">Business</option>
                <option value="Investment Returns">Investment Returns</option>
                <option value="Rental">Rental Income</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>

          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Source</th>
                  <th>Amount</th>
                  <th>Category</th>
                  <th>Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="incomeList">
                <!-- Income entries will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Expenses Page -->
      <div class="page" id="expenses-page">
        <div class="page-header">
          <h1>Expense Tracking</h1>
          <p>Monitor and categorize your spending</p>
        </div>

        <!-- Expense Summary Cards -->
        <div class="expense-summary">
          <div class="summary-card">
            <h4>This Month</h4>
            <p class="amount expense-amount" id="currentMonthExpenses">₹0.00</p>
          </div>
          <div class="summary-card">
            <h4>Budget Remaining</h4>
            <p class="amount" id="budgetRemaining">₹0.00</p>
          </div>
          <div class="summary-card">
            <h4>Average Daily</h4>
            <p class="amount expense-amount" id="avgDailyExpense">₹0.00</p>
          </div>
        </div>

        <!-- Add Expense Form -->
        <div class="form-section">
          <h3>Add Expense</h3>
          <form id="expenseForm" class="finance-form">
            <div class="form-row">
              <div class="form-control">
                <label for="expenseName">Expense Description</label>
                <input
                  type="text"
                  id="expenseName"
                  placeholder="Enter expense description"
                  required
                />
              </div>
              <div class="form-control">
                <label for="expenseAmount">Amount (₹)</label>
                <input
                  type="number"
                  id="expenseAmount"
                  placeholder="Enter amount"
                  step="1.00"
                  min="0"
                  required
                />
              </div>
            </div>
            <div class="form-row">
              <div class="form-control">
                <label for="expenseCategory">Category</label>
                <select id="expenseCategory" required>
                  <option value="">Select Category</option>
                  <option value="Food">Food</option>
                  <option value="Transport">Transport</option>
                  <option value="Entertainment">Entertainment</option>
                  <option value="Shopping">Shopping</option>
                  <option value="Bills">Bills</option>
                  <option value="Healthcare">Healthcare</option>
                  <option value="Education">Education</option>
                  <option value="Travel">Travel</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div class="form-control">
                <label for="expenseDate">Date</label>
                <input type="date" id="expenseDate" required />
              </div>
            </div>
            <button type="submit" class="btn primary">Add Expense</button>
          </form>
        </div>

        <!-- Expense History -->
        <div class="data-section">
          <div class="section-header">
            <h3>Expense History</h3>
            <div class="filter-controls">
              <select id="expenseFilter">
                <option value="all">All Categories</option>
                <option value="Food">Food</option>
                <option value="Transport">Transport</option>
                <option value="Entertainment">Entertainment</option>
                <option value="Shopping">Shopping</option>
                <option value="Bills">Bills</option>
                <option value="Healthcare">Healthcare</option>
                <option value="Education">Education</option>
                <option value="Travel">Travel</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>

          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Description</th>
                  <th>Amount</th>
                  <th>Category</th>
                  <th>Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="expenseList">
                <!-- Expense entries will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Investments Page -->
      <div class="page" id="investments-page">
        <div class="page-header">
          <h1>Investment Portfolio</h1>
          <p>Track your investments and portfolio performance</p>
        </div>

        <!-- Portfolio Summary -->
        <div class="portfolio-summary">
          <div class="summary-card">
            <h4>Total Portfolio Value</h4>
            <p class="amount" id="portfolioValue">₹0.00</p>
          </div>
          <div class="summary-card">
            <h4>Total Gains/Losses</h4>
            <p class="amount" id="portfolioGains">₹0.00</p>
          </div>
          <div class="summary-card">
            <h4>Portfolio Return</h4>
            <p class="amount" id="portfolioReturn">0.00%</p>
          </div>
        </div>

        <!-- Add Investment Form -->
        <div class="form-section">
          <h3>Add Investment</h3>
          <form id="investmentForm" class="finance-form">
            <div class="form-row">
              <div class="form-control">
                <label for="investmentName">Investment Name</label>
                <input
                  type="text"
                  id="investmentName"
                  placeholder="e.g., HDFC Mutual Fund"
                  required
                />
              </div>
              <div class="form-control">
                <label for="investmentType">Type</label>
                <select id="investmentType" required>
                  <option value="">Select Type</option>
                  <option value="Stocks">Stocks</option>
                  <option value="Mutual Funds">Mutual Funds</option>
                  <option value="Fixed Deposit">Fixed Deposit</option>
                  <option value="Cryptocurrency">Cryptocurrency</option>
                  <option value="Bonds">Bonds</option>
                  <option value="Real Estate">Real Estate</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-control">
                <label for="investmentAmount">Amount Invested (₹)</label>
                <input
                  type="number"
                  id="investmentAmount"
                  placeholder="Enter amount"
                  step="0.01"
                  min="0"
                  required
                />
              </div>
              <div class="form-control">
                <label for="currentValue">Current Value (₹)</label>
                <input
                  type="number"
                  id="currentValue"
                  placeholder="Enter current value"
                  step="0.01"
                  min="0"
                  required
                />
              </div>
            </div>
            <div class="form-row">
              <div class="form-control">
                <label for="investmentDate">Investment Date</label>
                <input type="date" id="investmentDate" required />
              </div>
            </div>
            <button type="submit" class="btn primary">Add Investment</button>
          </form>
        </div>

        <!-- Investment Portfolio -->
        <div class="data-section">
          <div class="section-header">
            <h3>Your Investments</h3>
            <div class="filter-controls">
              <select id="investmentFilter">
                <option value="all">All Types</option>
                <option value="Stocks">Stocks</option>
                <option value="Mutual Funds">Mutual Funds</option>
                <option value="Fixed Deposit">Fixed Deposit</option>
                <option value="Cryptocurrency">Cryptocurrency</option>
                <option value="Bonds">Bonds</option>
                <option value="Real Estate">Real Estate</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>

          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Investment</th>
                  <th>Type</th>
                  <th>Invested</th>
                  <th>Current Value</th>
                  <th>Gain/Loss</th>
                  <th>Return %</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="investmentList">
                <!-- Investment entries will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Budget Page -->
      <div class="page" id="budget-page">
        <div class="page-header">
          <h1>Budget Management</h1>
          <p>Set and track your financial goals</p>
        </div>

        <!-- Budget Overview -->
        <div class="budget-overview">
          <div class="budget-card">
            <h4>Monthly Budget</h4>
            <p class="amount" id="monthlyBudget">₹0.00</p>
          </div>
          <div class="budget-card">
            <h4>Spent This Month</h4>
            <p class="amount expense-amount" id="budgetSpent">₹0.00</p>
          </div>
          <div class="budget-card">
            <h4>Remaining</h4>
            <p class="amount" id="budgetLeft">₹0.00</p>
          </div>
        </div>

        <!-- Budget Categories -->
        <div class="budget-categories">
          <h3>Category Budgets</h3>
          <div id="categoryBudgets">
            <!-- Category budget items will be populated here -->
          </div>
        </div>
      </div>

      <!-- Credit Card Import Page -->
      <div class="page" id="credit-import-page">
        <div class="page-header">
          <h1>Credit Card Import</h1>
          <p>Import transactions from your credit card PDF statements</p>
        </div>

        <!-- Import Status Cards -->
        <div class="import-summary">
          <div class="summary-card">
            <h4>Last Import</h4>
            <p class="amount" id="lastImportDate">Never</p>
          </div>
          <div class="summary-card">
            <h4>Transactions Imported</h4>
            <p class="amount" id="totalImported">0</p>
          </div>
          <div class="summary-card">
            <h4>Import Success Rate</h4>
            <p class="amount" id="importSuccessRate">0%</p>
          </div>
        </div>

        <!-- PDF Upload Section -->
        <div class="form-section">
          <h3>Upload Credit Card Statement</h3>
          <div class="upload-container">
            <div class="file-upload-area" id="fileUploadArea">
              <div class="upload-icon">📄</div>
              <div class="upload-text">
                <p><strong>Click to upload</strong> or drag and drop</p>
                <p>PDF files only (Max 10MB)</p>
              </div>
              <input type="file" id="pdfFileInput" accept=".pdf" hidden />
            </div>

            <div
              class="upload-options"
              id="uploadOptions"
              style="display: none"
            >
              <div class="form-control">
                <label for="pdfPassword">PDF Password (if required)</label>
                <input
                  type="password"
                  id="pdfPassword"
                  placeholder="Enter PDF password"
                />
              </div>

              <div class="form-control">
                <label for="statementType">Statement Type</label>
                <select id="statementType">
                  <option value="auto">Auto-detect</option>
                  <option value="hdfc">HDFC Bank</option>
                  <option value="icici">ICICI Bank</option>
                  <option value="sbi">SBI</option>
                  <option value="axis">Axis Bank</option>
                  <option value="generic">Generic Format</option>
                </select>
              </div>

              <button type="button" class="btn primary" id="processPdfBtn">
                <span class="btn-text">Process PDF</span>
                <span class="btn-loader" style="display: none"
                  >Processing...</span
                >
              </button>
            </div>
          </div>
        </div>

        <!-- Processing Status -->
        <div
          class="processing-status"
          id="processingStatus"
          style="display: none"
        >
          <div class="status-header">
            <h3>Processing Status</h3>
            <div class="progress-bar">
              <div class="progress-fill" id="progressFill"></div>
            </div>
          </div>
          <div class="status-messages" id="statusMessages">
            <!-- Status messages will be populated here -->
          </div>
        </div>

        <!-- Preview and Categorization -->
        <div class="preview-section" id="previewSection" style="display: none">
          <div class="section-header">
            <h3>Transaction Preview</h3>
            <div class="preview-actions">
              <button type="button" class="btn secondary" id="selectAllBtn">
                Select All
              </button>
              <button type="button" class="btn secondary" id="deselectAllBtn">
                Deselect All
              </button>
              <button type="button" class="btn primary" id="importSelectedBtn">
                Import Selected
              </button>
            </div>
          </div>

          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th><input type="checkbox" id="selectAllCheckbox" /></th>
                  <th>Date</th>
                  <th>Description</th>
                  <th>Amount</th>
                  <th>Category</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody id="previewTransactionsList">
                <!-- Preview transactions will be populated here -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Import History -->
        <div class="data-section">
          <div class="section-header">
            <h3>Import History</h3>
            <div class="filter-controls">
              <select id="importHistoryFilter">
                <option value="all">All Imports</option>
                <option value="success">Successful</option>
                <option value="failed">Failed</option>
                <option value="partial">Partial</option>
              </select>
            </div>
          </div>

          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>File Name</th>
                  <th>Transactions Found</th>
                  <th>Imported</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="importHistoryList">
                <!-- Import history will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </main>

    <!-- PDF.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <!-- JavaScript -->
    <script src="app.js"></script>
  </body>
</html>
