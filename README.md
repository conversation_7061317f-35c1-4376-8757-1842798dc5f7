# Personal Finance Manager

A comprehensive web-based personal finance management application with advanced features including credit card statement import, expense tracking, income management, investment portfolio tracking, and budget planning.

## Features

### 🏠 Overview Dashboard
- Real-time financial metrics and key performance indicators
- Interactive charts showing spending patterns, income vs expenses, net worth trends, and investment portfolio
- Recent transactions summary
- Comprehensive financial snapshot at a glance

### 💵 Income Management
- Track multiple income sources (salary, freelance, business, investments, rental)
- Monthly income summaries and trends
- Recurring income setup
- Income categorization and filtering

### 💸 Expense Tracking
- Detailed expense categorization (Food, Transport, Entertainment, Shopping, Bills, Healthcare, Education, Travel)
- Monthly expense summaries and budget tracking
- Average daily spending calculations
- Expense history with filtering capabilities

### 📈 Investment Portfolio
- Track various investment types (Stocks, Mutual Funds, Fixed Deposits, Cryptocurrency, Bonds, Real Estate)
- Portfolio performance monitoring
- Gain/loss calculations and return percentages
- Investment diversification overview

### 🎯 Budget Management
- Set monthly budgets and track spending against targets
- Category-wise budget allocation
- Budget remaining calculations
- Visual budget progress indicators

### 💳 Credit Card Import (NEW!)
- **PDF Statement Processing**: Upload credit card PDF statements for automatic transaction extraction
- **Password Protection Support**: Handle password-protected PDF files
- **Multi-Bank Support**: Optimized parsers for HDFC, ICICI, SBI, Axis Bank, and generic formats
- **Smart Categorization**: Automatic transaction categorization based on merchant names
- **Duplicate Detection**: Identify and prevent duplicate transaction imports
- **Preview & Edit**: Review transactions before importing with category editing capabilities
- **Import History**: Track all import activities with success rates and statistics
- **Batch Operations**: Select/deselect multiple transactions for import

## Credit Card Import Usage

### Step 1: Access the Feature
1. Click on the "💳 Credit Card Import" option in the sidebar
2. The page displays import statistics and upload interface

### Step 2: Upload PDF Statement
1. Click the upload area or drag and drop your PDF credit card statement
2. Supported file size: Up to 10MB
3. If the PDF is password-protected, enter the password in the provided field
4. Select your bank type for optimized parsing (or use Auto-detect)

### Step 3: Process the PDF
1. Click "Process PDF" to start extraction
2. Monitor the progress bar and status messages
3. The system will:
   - Extract text from all PDF pages
   - Identify transaction tables or patterns
   - Parse dates, descriptions, and amounts
   - Apply bank-specific parsing rules

### Step 4: Review and Categorize
1. Preview extracted transactions in the table
2. Transactions are automatically categorized based on merchant names
3. Duplicate transactions are flagged and unchecked by default
4. Edit categories using the dropdown menus
5. Select/deselect transactions for import

### Step 5: Import Transactions
1. Use "Select All" or "Deselect All" for bulk operations
2. Click "Import Selected" to add transactions to your expense history
3. View import summary and success message
4. Check the Import History section for records

### Supported PDF Formats
- **Table-based statements**: PDFs with clear table structures containing Date, Transaction Details, and Amount columns
- **Pattern-based statements**: PDFs with consistent transaction line formats
- **Bank-specific formats**: Optimized parsing for major Indian banks

### Troubleshooting
- **No transactions found**: Ensure the PDF contains a clear transaction table or consistent formatting
- **Incorrect parsing**: Try selecting a specific bank type instead of Auto-detect
- **Password errors**: Verify the PDF password is correct
- **Large files**: Compress the PDF if it exceeds 10MB

## Technical Features

### 🔧 Modern Architecture
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Local Storage**: All data stored locally in your browser for privacy
- **Real-time Updates**: Instant data synchronization across all pages
- **Progressive Enhancement**: Graceful degradation for older browsers

### 🎨 User Interface
- **Collapsible Sidebar**: Toggle sidebar for more screen space
- **Rounded Design**: Modern rounded borders and smooth transitions
- **Interactive Charts**: Powered by Chart.js for beautiful data visualization
- **Status Indicators**: Real-time feedback for all operations

### 🔒 Security & Privacy
- **Client-side Processing**: All PDF processing happens in your browser
- **No Data Upload**: Your financial data never leaves your device
- **Password Handling**: PDF passwords are processed locally and not stored

## Getting Started

1. **Open the Application**: Open `index.html` in your web browser
2. **Add Initial Data**: Start by adding some income and expense entries
3. **Import Statements**: Use the Credit Card Import feature to bulk import transactions
4. **Set Budgets**: Configure monthly budgets for different categories
5. **Track Investments**: Add your investment portfolio for comprehensive tracking

## Browser Compatibility

- **Chrome**: Full support (recommended)
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **Mobile Browsers**: Responsive design works on all modern mobile browsers

## File Structure

```
Finance Planner/
├── index.html              # Main application file
├── app.js                  # Core application logic and PDF processing
├── styles.css              # Comprehensive styling
├── expensetracker.html     # Standalone expense tracker
├── expensetrackers.css     # Expense tracker styles
├── trackerscript.js        # Expense tracker logic
├── sample-credit-statement.txt  # Sample data for testing
└── README.md               # This documentation
```

## Dependencies

- **PDF.js**: For PDF processing and text extraction
- **Chart.js**: For interactive charts and data visualization
- **Inter Font**: Modern typography from Google Fonts

## Future Enhancements

- Bank statement import (beyond credit cards)
- Export functionality (CSV, PDF reports)
- Advanced analytics and insights
- Multi-currency support
- Cloud synchronization options
- Mobile app development

## Support

For issues or feature requests, please refer to the application's built-in help system or check the console for debugging information.

---

**Note**: This application processes all data locally in your browser. No financial information is transmitted to external servers, ensuring complete privacy and security of your financial data.
