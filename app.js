// Personal Finance Manager - Comprehensive JavaScript Application
class FinanceManager {
  constructor() {
    this.data = {
      income: JSON.parse(localStorage.getItem("financeIncome")) || [],
      expenses: JSON.parse(localStorage.getItem("financeExpenses")) || [],
      investments: JSON.parse(localStorage.getItem("financeInvestments")) || [],
      budgets:
        JSON.parse(localStorage.getItem("financeBudgets")) ||
        this.getDefaultBudgets(),
      creditImports:
        JSON.parse(localStorage.getItem("financeCreditImports")) || [],
    };

    this.currentPage = "overview";
    this.charts = {};
    this.pdfProcessor = new PDFProcessor();

    this.init();
  }

  init() {
    this.setupNavigation();
    this.setupEventListeners();
    this.loadPage("overview");
    this.updateAllData();
  }

  // Navigation System
  setupNavigation() {
    const navLinks = document.querySelectorAll(".nav-link");
    const sidebarToggle = document.getElementById("sidebarToggle");
    const sidebar = document.getElementById("sidebar");
    const mainContent = document.getElementById("mainContent");

    navLinks.forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        const page = link.dataset.page;
        this.loadPage(page);

        // Update active nav
        navLinks.forEach((l) => l.classList.remove("active"));
        link.classList.add("active");
      });
    });

    // Sidebar toggle functionality
    if (sidebarToggle) {
      sidebarToggle.addEventListener("click", () => {
        sidebar.classList.toggle("collapsed");
        // For mobile, use 'open' class instead
        if (window.innerWidth <= 768) {
          sidebar.classList.toggle("open");
        }
      });
    }

    // Close sidebar on mobile when clicking outside
    if (window.innerWidth <= 768) {
      mainContent.addEventListener("click", () => {
        sidebar.classList.remove("open");
      });
    }
  }

  loadPage(pageName) {
    // Hide all pages
    document.querySelectorAll(".page").forEach((page) => {
      page.classList.remove("active");
    });

    // Show selected page
    const targetPage = document.getElementById(`${pageName}-page`);
    if (targetPage) {
      targetPage.classList.add("active");
      this.currentPage = pageName;

      // Load page-specific data
      this.loadPageData(pageName);
    }
  }

  loadPageData(pageName) {
    switch (pageName) {
      case "overview":
        this.updateOverviewDashboard();
        break;
      case "income":
        this.updateIncomeData();
        break;
      case "expenses":
        this.updateExpenseData();
        break;
      case "investments":
        this.updateInvestmentData();
        break;
      case "budget":
        this.updateBudgetData();
        break;
      case "credit-import":
        this.updateCreditImportData();
        break;
    }
  }

  // Event Listeners Setup
  setupEventListeners() {
    // Income form
    const incomeForm = document.getElementById("incomeForm");
    if (incomeForm) {
      incomeForm.addEventListener("submit", (e) => this.handleIncomeSubmit(e));
    }

    // Expense form
    const expenseForm = document.getElementById("expenseForm");
    if (expenseForm) {
      expenseForm.addEventListener("submit", (e) =>
        this.handleExpenseSubmit(e)
      );
    }

    // Investment form
    const investmentForm = document.getElementById("investmentForm");
    if (investmentForm) {
      investmentForm.addEventListener("submit", (e) =>
        this.handleInvestmentSubmit(e)
      );
    }

    // Filter listeners
    this.setupFilterListeners();

    // Set default dates
    this.setDefaultDates();
  }

  setDefaultDates() {
    const today = new Date().toISOString().split("T")[0];
    const dateInputs = ["incomeDate", "expenseDate", "investmentDate"];

    dateInputs.forEach((id) => {
      const input = document.getElementById(id);
      if (input) input.value = today;
    });
  }

  setupFilterListeners() {
    const filters = ["incomeFilter", "expenseFilter", "investmentFilter"];

    filters.forEach((filterId) => {
      const filter = document.getElementById(filterId);
      if (filter) {
        filter.addEventListener("change", () => {
          const type = filterId.replace("Filter", "");
          this.filterData(type, filter.value);
        });
      }
    });
  }

  // Data Management
  saveData() {
    localStorage.setItem("financeIncome", JSON.stringify(this.data.income));
    localStorage.setItem("financeExpenses", JSON.stringify(this.data.expenses));
    localStorage.setItem(
      "financeInvestments",
      JSON.stringify(this.data.investments)
    );
    localStorage.setItem("financeBudgets", JSON.stringify(this.data.budgets));
    localStorage.setItem(
      "financeCreditImports",
      JSON.stringify(this.data.creditImports)
    );
  }

  updateAllData() {
    this.updateOverviewDashboard();
    this.updateIncomeData();
    this.updateExpenseData();
    this.updateInvestmentData();
    this.updateBudgetData();
  }

  // Income Management
  handleIncomeSubmit(e) {
    e.preventDefault();

    const formData = {
      id: Date.now(),
      source: document.getElementById("incomeSource").value,
      amount: parseFloat(document.getElementById("incomeAmount").value),
      category: document.getElementById("incomeCategory").value,
      date: document.getElementById("incomeDate").value,
      recurring: document.getElementById("incomeRecurring").checked,
      timestamp: new Date().toISOString(),
    };

    this.data.income.push(formData);
    this.saveData();
    this.updateAllData();

    // Reset form
    e.target.reset();
    this.setDefaultDates();

    this.showMessage("Income added successfully!", "success");
  }

  updateIncomeData() {
    this.renderIncomeList();
    this.updateIncomeSummary();
  }

  renderIncomeList(filteredData = null) {
    const incomeList = document.getElementById("incomeList");
    if (!incomeList) return;

    const data = filteredData || this.data.income;

    incomeList.innerHTML = data
      .map(
        (income) => `
      <tr>
        <td>${income.source}</td>
        <td class="amount">₹${income.amount.toLocaleString("en-IN")}</td>
        <td><span class="category-badge category-${income.category
          .toLowerCase()
          .replace(" ", "")}">${income.category}</span></td>
        <td>${this.formatDate(income.date)}</td>
        <td>
          <div class="action-buttons">
            <button class="edit-btn" onclick="financeManager.editIncome(${
              income.id
            })">Edit</button>
            <button class="delete-btn" onclick="financeManager.deleteIncome(${
              income.id
            })">Delete</button>
          </div>
        </td>
      </tr>
    `
      )
      .join("");
  }

  updateIncomeSummary() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const currentMonthIncome = this.data.income
      .filter((income) => {
        const incomeDate = new Date(income.date);
        return (
          incomeDate.getMonth() === currentMonth &&
          incomeDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, income) => sum + income.amount, 0);

    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    const lastMonthIncome = this.data.income
      .filter((income) => {
        const incomeDate = new Date(income.date);
        return (
          incomeDate.getMonth() === lastMonth &&
          incomeDate.getFullYear() === lastMonthYear
        );
      })
      .reduce((sum, income) => sum + income.amount, 0);

    const avgMonthlyIncome =
      this.data.income.length > 0
        ? this.data.income.reduce((sum, income) => sum + income.amount, 0) /
          Math.max(1, this.getMonthsCount())
        : 0;

    this.updateElement(
      "currentMonthIncome",
      `₹${currentMonthIncome.toLocaleString("en-IN")}`
    );
    this.updateElement(
      "lastMonthIncome",
      `₹${lastMonthIncome.toLocaleString("en-IN")}`
    );
    this.updateElement(
      "avgMonthlyIncome",
      `₹${avgMonthlyIncome.toLocaleString("en-IN")}`
    );
  }

  // Expense Management
  handleExpenseSubmit(e) {
    e.preventDefault();

    const formData = {
      id: Date.now(),
      text: document.getElementById("expenseName").value,
      amount: parseFloat(document.getElementById("expenseAmount").value),
      category: document.getElementById("expenseCategory").value,
      date: document.getElementById("expenseDate").value,
      timestamp: new Date().toISOString(),
    };

    this.data.expenses.push(formData);
    this.saveData();
    this.updateAllData();

    // Reset form
    e.target.reset();
    this.setDefaultDates();

    this.showMessage("Expense added successfully!", "success");
  }

  updateExpenseData() {
    this.renderExpenseList();
    this.updateExpenseSummary();
  }

  renderExpenseList(filteredData = null) {
    const expenseList = document.getElementById("expenseList");
    if (!expenseList) return;

    const data = filteredData || this.data.expenses;

    expenseList.innerHTML = data
      .map(
        (expense) => `
      <tr>
        <td>${expense.text}</td>
        <td class="amount expense-amount">₹${expense.amount.toLocaleString(
          "en-IN"
        )}</td>
        <td><span class="category-badge category-${expense.category.toLowerCase()}">${
          expense.category
        }</span></td>
        <td>${this.formatDate(expense.date)}</td>
        <td>
          <div class="action-buttons">
            <button class="edit-btn" onclick="financeManager.editExpense(${
              expense.id
            })">Edit</button>
            <button class="delete-btn" onclick="financeManager.deleteExpense(${
              expense.id
            })">Delete</button>
          </div>
        </td>
      </tr>
    `
      )
      .join("");
  }

  updateExpenseSummary() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const currentMonthExpenses = this.data.expenses
      .filter((expense) => {
        const expenseDate = new Date(expense.date);
        return (
          expenseDate.getMonth() === currentMonth &&
          expenseDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, expense) => sum + expense.amount, 0);

    const monthlyBudget = this.data.budgets.monthly || 50000;
    const budgetRemaining = monthlyBudget - currentMonthExpenses;

    const avgDailyExpense =
      currentMonthExpenses / Math.max(1, new Date().getDate());

    this.updateElement(
      "currentMonthExpenses",
      `₹${currentMonthExpenses.toLocaleString("en-IN")}`
    );
    this.updateElement(
      "budgetRemaining",
      `₹${budgetRemaining.toLocaleString("en-IN")}`
    );
    this.updateElement(
      "avgDailyExpense",
      `₹${avgDailyExpense.toLocaleString("en-IN")}`
    );
  }

  // Investment Management
  handleInvestmentSubmit(e) {
    e.preventDefault();

    const invested = parseFloat(
      document.getElementById("investmentAmount").value
    );
    const current = parseFloat(document.getElementById("currentValue").value);

    const formData = {
      id: Date.now(),
      name: document.getElementById("investmentName").value,
      type: document.getElementById("investmentType").value,
      invested: invested,
      currentValue: current,
      gainLoss: current - invested,
      returnPercent: (((current - invested) / invested) * 100).toFixed(2),
      date: document.getElementById("investmentDate").value,
      timestamp: new Date().toISOString(),
    };

    this.data.investments.push(formData);
    this.saveData();
    this.updateAllData();

    // Reset form
    e.target.reset();
    this.setDefaultDates();

    this.showMessage("Investment added successfully!", "success");
  }

  updateInvestmentData() {
    this.renderInvestmentList();
    this.updateInvestmentSummary();
  }

  renderInvestmentList(filteredData = null) {
    const investmentList = document.getElementById("investmentList");
    if (!investmentList) return;

    const data = filteredData || this.data.investments;

    investmentList.innerHTML = data
      .map(
        (investment) => `
      <tr>
        <td>${investment.name}</td>
        <td><span class="category-badge category-${investment.type
          .toLowerCase()
          .replace(" ", "")}">${investment.type}</span></td>
        <td class="amount">₹${investment.invested.toLocaleString("en-IN")}</td>
        <td class="amount">₹${investment.currentValue.toLocaleString(
          "en-IN"
        )}</td>
        <td class="amount ${
          investment.gainLoss >= 0 ? "positive" : "negative"
        }">₹${investment.gainLoss.toLocaleString("en-IN")}</td>
        <td class="amount ${
          investment.gainLoss >= 0 ? "positive" : "negative"
        }">${investment.returnPercent}%</td>
        <td>
          <div class="action-buttons">
            <button class="edit-btn" onclick="financeManager.editInvestment(${
              investment.id
            })">Edit</button>
            <button class="delete-btn" onclick="financeManager.deleteInvestment(${
              investment.id
            })">Delete</button>
          </div>
        </td>
      </tr>
    `
      )
      .join("");
  }

  updateInvestmentSummary() {
    const totalInvested = this.data.investments.reduce(
      (sum, inv) => sum + inv.invested,
      0
    );
    const totalCurrent = this.data.investments.reduce(
      (sum, inv) => sum + inv.currentValue,
      0
    );
    const totalGains = totalCurrent - totalInvested;
    const totalReturn =
      totalInvested > 0 ? ((totalGains / totalInvested) * 100).toFixed(2) : 0;

    this.updateElement(
      "portfolioValue",
      `₹${totalCurrent.toLocaleString("en-IN")}`
    );
    this.updateElement(
      "portfolioGains",
      `₹${totalGains.toLocaleString("en-IN")}`
    );
    this.updateElement("portfolioReturn", `${totalReturn}%`);
  }

  // Overview Dashboard
  updateOverviewDashboard() {
    this.updateMetrics();
    this.updateCharts();
    this.updateRecentTransactions();
  }

  updateMetrics() {
    const totalIncome = this.data.income.reduce(
      (sum, income) => sum + income.amount,
      0
    );
    const totalExpenses = this.data.expenses.reduce(
      (sum, expense) => sum + expense.amount,
      0
    );
    const totalInvestments = this.data.investments.reduce(
      (sum, inv) => sum + inv.currentValue,
      0
    );
    const netWorth = totalIncome - totalExpenses + totalInvestments;

    // Get monthly data
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyIncome = this.data.income
      .filter((income) => {
        const date = new Date(income.date);
        return (
          date.getMonth() === currentMonth && date.getFullYear() === currentYear
        );
      })
      .reduce((sum, income) => sum + income.amount, 0);

    const monthlyExpenses = this.data.expenses
      .filter((expense) => {
        const date = new Date(expense.date);
        return (
          date.getMonth() === currentMonth && date.getFullYear() === currentYear
        );
      })
      .reduce((sum, expense) => sum + expense.amount, 0);

    this.updateElement("netWorth", `₹${netWorth.toLocaleString("en-IN")}`);
    this.updateElement(
      "monthlyIncome",
      `₹${monthlyIncome.toLocaleString("en-IN")}`
    );
    this.updateElement(
      "monthlyExpenses",
      `₹${monthlyExpenses.toLocaleString("en-IN")}`
    );
    this.updateElement(
      "totalInvestments",
      `₹${totalInvestments.toLocaleString("en-IN")}`
    );
  }

  updateCharts() {
    this.createExpenseChart();
    this.createIncomeExpenseChart();
    this.createNetWorthChart();
    this.createPortfolioChart();
  }

  createExpenseChart() {
    const ctx = document.getElementById("expenseChart");
    if (!ctx) return;

    // Destroy existing chart
    if (this.charts.expense) {
      this.charts.expense.destroy();
    }

    // Group expenses by category
    const categoryData = {};
    this.data.expenses.forEach((expense) => {
      categoryData[expense.category] =
        (categoryData[expense.category] || 0) + expense.amount;
    });

    const labels = Object.keys(categoryData);
    const data = Object.values(categoryData);
    const colors = [
      "#ef4444",
      "#f59e0b",
      "#10b981",
      "#3b82f6",
      "#8b5cf6",
      "#f97316",
      "#06b6d4",
      "#84cc16",
      "#ec4899",
      "#6b7280",
    ];

    this.charts.expense = new Chart(ctx, {
      type: "doughnut",
      data: {
        labels: labels,
        datasets: [
          {
            data: data,
            backgroundColor: colors.slice(0, labels.length),
            borderWidth: 2,
            borderColor: "#ffffff",
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "bottom",
          },
        },
      },
    });
  }

  createIncomeExpenseChart() {
    const ctx = document.getElementById("incomeExpenseChart");
    if (!ctx) return;

    if (this.charts.incomeExpense) {
      this.charts.incomeExpense.destroy();
    }

    // Get last 6 months data
    const months = [];
    const incomeData = [];
    const expenseData = [];

    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const month = date.getMonth();
      const year = date.getFullYear();

      months.push(date.toLocaleDateString("en-IN", { month: "short" }));

      const monthIncome = this.data.income
        .filter((income) => {
          const incomeDate = new Date(income.date);
          return (
            incomeDate.getMonth() === month && incomeDate.getFullYear() === year
          );
        })
        .reduce((sum, income) => sum + income.amount, 0);

      const monthExpense = this.data.expenses
        .filter((expense) => {
          const expenseDate = new Date(expense.date);
          return (
            expenseDate.getMonth() === month &&
            expenseDate.getFullYear() === year
          );
        })
        .reduce((sum, expense) => sum + expense.amount, 0);

      incomeData.push(monthIncome);
      expenseData.push(monthExpense);
    }

    this.charts.incomeExpense = new Chart(ctx, {
      type: "bar",
      data: {
        labels: months,
        datasets: [
          {
            label: "Income",
            data: incomeData,
            backgroundColor: "#10b981",
            borderColor: "#059669",
            borderWidth: 1,
          },
          {
            label: "Expenses",
            data: expenseData,
            backgroundColor: "#ef4444",
            borderColor: "#dc2626",
            borderWidth: 1,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function (value) {
                return "₹" + value.toLocaleString("en-IN");
              },
            },
          },
        },
        plugins: {
          legend: {
            position: "top",
          },
        },
      },
    });
  }

  createNetWorthChart() {
    const ctx = document.getElementById("netWorthChart");
    if (!ctx) return;

    if (this.charts.netWorth) {
      this.charts.netWorth.destroy();
    }

    // Calculate net worth over time (simplified)
    const months = [];
    const netWorthData = [];

    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      months.push(date.toLocaleDateString("en-IN", { month: "short" }));

      // Simplified calculation - in real app, you'd track this over time
      const baseNetWorth = 100000 + i * 5000; // Simulated growth
      netWorthData.push(baseNetWorth);
    }

    this.charts.netWorth = new Chart(ctx, {
      type: "line",
      data: {
        labels: months,
        datasets: [
          {
            label: "Net Worth",
            data: netWorthData,
            borderColor: "#3b82f6",
            backgroundColor: "rgba(59, 130, 246, 0.1)",
            borderWidth: 3,
            fill: true,
            tension: 0.4,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: false,
            ticks: {
              callback: function (value) {
                return "₹" + value.toLocaleString("en-IN");
              },
            },
          },
        },
        plugins: {
          legend: {
            display: false,
          },
        },
      },
    });
  }

  createPortfolioChart() {
    const ctx = document.getElementById("portfolioChart");
    if (!ctx) return;

    if (this.charts.portfolio) {
      this.charts.portfolio.destroy();
    }

    // Group investments by type
    const typeData = {};
    this.data.investments.forEach((investment) => {
      typeData[investment.type] =
        (typeData[investment.type] || 0) + investment.currentValue;
    });

    const labels = Object.keys(typeData);
    const data = Object.values(typeData);
    const colors = ["#8b5cf6", "#06b6d4", "#f59e0b", "#10b981", "#ef4444"];

    this.charts.portfolio = new Chart(ctx, {
      type: "pie",
      data: {
        labels: labels,
        datasets: [
          {
            data: data,
            backgroundColor: colors.slice(0, labels.length),
            borderWidth: 2,
            borderColor: "#ffffff",
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "bottom",
          },
        },
      },
    });
  }

  updateRecentTransactions() {
    const container = document.getElementById("recentTransactions");
    if (!container) return;

    // Combine and sort all transactions
    const allTransactions = [
      ...this.data.income.map((item) => ({ ...item, type: "income" })),
      ...this.data.expenses.map((item) => ({ ...item, type: "expense" })),
    ]
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 5);

    container.innerHTML = allTransactions
      .map(
        (transaction) => `
      <div class="transaction-item ${transaction.type}">
        <div>
          <strong>${transaction.source || transaction.text}</strong>
          <br>
          <small>${this.formatDate(transaction.date)} • ${
          transaction.category
        }</small>
        </div>
        <div class="amount ${
          transaction.type === "expense" ? "expense-amount" : ""
        }">
          ${
            transaction.type === "income" ? "+" : "-"
          }₹${transaction.amount.toLocaleString("en-IN")}
        </div>
      </div>
    `
      )
      .join("");
  }

  // Budget Management
  updateBudgetData() {
    this.updateBudgetOverview();
    this.updateCategoryBudgets();
  }

  updateBudgetOverview() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyBudget = this.data.budgets.monthly || 50000;
    const spent = this.data.expenses
      .filter((expense) => {
        const date = new Date(expense.date);
        return (
          date.getMonth() === currentMonth && date.getFullYear() === currentYear
        );
      })
      .reduce((sum, expense) => sum + expense.amount, 0);

    const remaining = monthlyBudget - spent;

    this.updateElement(
      "monthlyBudget",
      `₹${monthlyBudget.toLocaleString("en-IN")}`
    );
    this.updateElement("budgetSpent", `₹${spent.toLocaleString("en-IN")}`);
    this.updateElement("budgetLeft", `₹${remaining.toLocaleString("en-IN")}`);
  }

  updateCategoryBudgets() {
    const container = document.getElementById("categoryBudgets");
    if (!container) return;

    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const categoryBudgets = this.data.budgets.categories || {};

    container.innerHTML = Object.entries(categoryBudgets)
      .map(([category, budget]) => {
        const spent = this.data.expenses
          .filter((expense) => {
            const date = new Date(expense.date);
            return (
              expense.category === category &&
              date.getMonth() === currentMonth &&
              date.getFullYear() === currentYear
            );
          })
          .reduce((sum, expense) => sum + expense.amount, 0);

        const percentage = Math.min((spent / budget) * 100, 100);
        const isOverBudget = spent > budget;

        return `
        <div class="budget-item">
          <div>
            <strong>${category}</strong>
            <br>
            <small>₹${spent.toLocaleString("en-IN")} / ₹${budget.toLocaleString(
          "en-IN"
        )}</small>
          </div>
          <div class="budget-progress">
            <div class="budget-progress-bar ${
              isOverBudget ? "over-budget" : ""
            }"
                 style="width: ${percentage}%"></div>
          </div>
        </div>
      `;
      })
      .join("");
  }

  // Utility Functions
  updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) element.textContent = value;
  }

  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString("en-IN");
  }

  getMonthsCount() {
    if (this.data.income.length === 0) return 1;

    const dates = this.data.income.map((income) => new Date(income.date));
    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));

    return Math.max(
      1,
      (maxDate.getFullYear() - minDate.getFullYear()) * 12 +
        maxDate.getMonth() -
        minDate.getMonth() +
        1
    );
  }

  getDefaultBudgets() {
    return {
      monthly: 50000,
      categories: {
        Food: 15000,
        Transport: 8000,
        Entertainment: 5000,
        Shopping: 10000,
        Bills: 12000,
        Healthcare: 5000,
        Education: 3000,
        Travel: 7000,
        Other: 5000,
      },
    };
  }

  showMessage(text, type) {
    // Create message element
    const message = document.createElement("div");
    message.className = `message ${type}`;
    message.textContent = text;

    // Insert at top of main content
    const mainContent = document.getElementById("mainContent");
    mainContent.insertBefore(message, mainContent.firstChild);

    // Auto remove after 3 seconds
    setTimeout(() => {
      if (message.parentNode) {
        message.remove();
      }
    }, 3000);
  }

  // Filter functionality
  filterData(type, category) {
    if (category === "all") {
      this[`render${type.charAt(0).toUpperCase() + type.slice(1)}List`]();
    } else {
      const filteredData = this.data[
        type === "expense" ? "expenses" : type
      ].filter((item) => item.category === category);
      this[`render${type.charAt(0).toUpperCase() + type.slice(1)}List`](
        filteredData
      );
    }
  }

  // Delete functions
  deleteIncome(id) {
    if (confirm("Are you sure you want to delete this income entry?")) {
      this.data.income = this.data.income.filter((income) => income.id !== id);
      this.saveData();
      this.updateAllData();
      this.showMessage("Income deleted successfully!", "success");
    }
  }

  deleteExpense(id) {
    if (confirm("Are you sure you want to delete this expense?")) {
      this.data.expenses = this.data.expenses.filter(
        (expense) => expense.id !== id
      );
      this.saveData();
      this.updateAllData();
      this.showMessage("Expense deleted successfully!", "success");
    }
  }

  deleteInvestment(id) {
    if (confirm("Are you sure you want to delete this investment?")) {
      this.data.investments = this.data.investments.filter(
        (investment) => investment.id !== id
      );
      this.saveData();
      this.updateAllData();
      this.showMessage("Investment deleted successfully!", "success");
    }
  }

  // Credit Card Import Management
  updateCreditImportData() {
    this.renderImportSummary();
    this.renderImportHistory();
    this.setupCreditImportEventListeners();
  }

  setupCreditImportEventListeners() {
    const fileUploadArea = document.getElementById("fileUploadArea");
    const pdfFileInput = document.getElementById("pdfFileInput");
    const processPdfBtn = document.getElementById("processPdfBtn");
    const selectAllBtn = document.getElementById("selectAllBtn");
    const deselectAllBtn = document.getElementById("deselectAllBtn");
    const importSelectedBtn = document.getElementById("importSelectedBtn");
    const selectAllCheckbox = document.getElementById("selectAllCheckbox");

    if (!fileUploadArea) return;

    // File upload area click
    fileUploadArea.addEventListener("click", () => {
      pdfFileInput.click();
    });

    // Drag and drop functionality
    fileUploadArea.addEventListener("dragover", (e) => {
      e.preventDefault();
      fileUploadArea.classList.add("dragover");
    });

    fileUploadArea.addEventListener("dragleave", () => {
      fileUploadArea.classList.remove("dragover");
    });

    fileUploadArea.addEventListener("drop", (e) => {
      e.preventDefault();
      fileUploadArea.classList.remove("dragover");
      const files = e.dataTransfer.files;
      if (files.length > 0 && files[0].type === "application/pdf") {
        pdfFileInput.files = files;
        this.handleFileSelection(files[0]);
      }
    });

    // File input change
    pdfFileInput.addEventListener("change", (e) => {
      if (e.target.files.length > 0) {
        this.handleFileSelection(e.target.files[0]);
      }
    });

    // Process PDF button
    if (processPdfBtn) {
      processPdfBtn.addEventListener("click", () => {
        this.processPDF();
      });
    }

    // Preview action buttons
    if (selectAllBtn) {
      selectAllBtn.addEventListener("click", () => {
        this.selectAllTransactions(true);
      });
    }

    if (deselectAllBtn) {
      deselectAllBtn.addEventListener("click", () => {
        this.selectAllTransactions(false);
      });
    }

    if (importSelectedBtn) {
      importSelectedBtn.addEventListener("click", () => {
        this.importSelectedTransactions();
      });
    }

    if (selectAllCheckbox) {
      selectAllCheckbox.addEventListener("change", (e) => {
        this.selectAllTransactions(e.target.checked);
      });
    }
  }

  handleFileSelection(file) {
    if (file.size > 10 * 1024 * 1024) {
      // 10MB limit
      this.showMessage("File size exceeds 10MB limit", "error");
      return;
    }

    const uploadOptions = document.getElementById("uploadOptions");
    const fileUploadArea = document.getElementById("fileUploadArea");

    // Update upload area to show selected file
    fileUploadArea.innerHTML = `
      <div class="upload-icon">📄</div>
      <div class="upload-text">
        <p><strong>${file.name}</strong></p>
        <p>Size: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
      </div>
    `;

    uploadOptions.style.display = "block";
    this.selectedFile = file;
  }

  async processPDF() {
    if (!this.selectedFile) {
      this.showMessage("Please select a PDF file first", "error");
      return;
    }

    const processPdfBtn = document.getElementById("processPdfBtn");
    const btnText = processPdfBtn.querySelector(".btn-text");
    const btnLoader = processPdfBtn.querySelector(".btn-loader");
    const processingStatus = document.getElementById("processingStatus");
    const progressFill = document.getElementById("progressFill");
    const statusMessages = document.getElementById("statusMessages");

    // Show processing UI
    btnText.style.display = "none";
    btnLoader.style.display = "inline-flex";
    processPdfBtn.disabled = true;
    processingStatus.style.display = "block";
    statusMessages.innerHTML = "";

    try {
      this.addStatusMessage("Starting PDF processing...", "info");
      progressFill.style.width = "10%";

      const password = document.getElementById("pdfPassword").value;
      const statementType = document.getElementById("statementType").value;

      this.addStatusMessage("Reading PDF file...", "info");
      progressFill.style.width = "30%";

      const transactions = await this.pdfProcessor.extractTransactions(
        this.selectedFile,
        password,
        statementType
      );

      this.addStatusMessage("Analyzing transactions...", "info");
      progressFill.style.width = "70%";

      if (transactions.length === 0) {
        throw new Error("No transactions found in the PDF");
      }

      this.addStatusMessage(
        `Found ${transactions.length} transactions`,
        "success"
      );
      progressFill.style.width = "100%";

      // Show preview
      this.showTransactionPreview(transactions);
      this.previewTransactions = transactions;
    } catch (error) {
      this.addStatusMessage(`Error: ${error.message}`, "error");
      console.error("PDF processing error:", error);
    } finally {
      // Reset button
      btnText.style.display = "inline";
      btnLoader.style.display = "none";
      processPdfBtn.disabled = false;
    }
  }

  addStatusMessage(message, type = "info") {
    const statusMessages = document.getElementById("statusMessages");
    if (!statusMessages) return;

    const messageElement = document.createElement("div");
    messageElement.className = `status-message ${type}`;
    messageElement.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
    statusMessages.appendChild(messageElement);
    statusMessages.scrollTop = statusMessages.scrollHeight;
  }

  showTransactionPreview(transactions) {
    const previewSection = document.getElementById("previewSection");
    const previewTransactionsList = document.getElementById(
      "previewTransactionsList"
    );

    if (!previewSection || !previewTransactionsList) return;

    previewSection.style.display = "block";

    previewTransactionsList.innerHTML = transactions
      .map((transaction, index) => {
        const category = this.categorizeTransaction(transaction.description);
        const isDuplicate = this.checkDuplicate(transaction);
        const status = isDuplicate ? "duplicate" : "ready";

        return `
        <tr class="preview-transaction-row" data-index="${index}">
          <td>
            <input type="checkbox" class="transaction-checkbox"
                   data-index="${index}" ${!isDuplicate ? "checked" : ""} />
          </td>
          <td>${this.formatDate(transaction.date)}</td>
          <td>${transaction.description}</td>
          <td class="amount expense-amount">₹${Math.abs(
            transaction.amount
          ).toLocaleString("en-IN")}</td>
          <td>
            <select class="category-select" data-index="${index}">
              <option value="Food" ${
                category === "Food" ? "selected" : ""
              }>Food</option>
              <option value="Transport" ${
                category === "Transport" ? "selected" : ""
              }>Transport</option>
              <option value="Entertainment" ${
                category === "Entertainment" ? "selected" : ""
              }>Entertainment</option>
              <option value="Shopping" ${
                category === "Shopping" ? "selected" : ""
              }>Shopping</option>
              <option value="Bills" ${
                category === "Bills" ? "selected" : ""
              }>Bills</option>
              <option value="Healthcare" ${
                category === "Healthcare" ? "selected" : ""
              }>Healthcare</option>
              <option value="Education" ${
                category === "Education" ? "selected" : ""
              }>Education</option>
              <option value="Travel" ${
                category === "Travel" ? "selected" : ""
              }>Travel</option>
              <option value="Other" ${
                category === "Other" ? "selected" : ""
              }>Other</option>
            </select>
          </td>
          <td><span class="status-badge ${status}">${
          isDuplicate ? "Duplicate" : "Ready"
        }</span></td>
        </tr>
      `;
      })
      .join("");

    // Add event listeners for checkboxes and category selects
    this.setupPreviewEventListeners();
  }

  setupPreviewEventListeners() {
    const checkboxes = document.querySelectorAll(".transaction-checkbox");
    const categorySelects = document.querySelectorAll(".category-select");

    checkboxes.forEach((checkbox) => {
      checkbox.addEventListener("change", () => {
        const row = checkbox.closest("tr");
        if (checkbox.checked) {
          row.classList.add("selected");
        } else {
          row.classList.remove("selected");
        }
        this.updateSelectAllCheckbox();
      });
    });

    categorySelects.forEach((select) => {
      select.addEventListener("change", (e) => {
        const index = parseInt(e.target.dataset.index);
        if (this.previewTransactions[index]) {
          this.previewTransactions[index].category = e.target.value;
        }
      });
    });
  }

  categorizeTransaction(description) {
    const desc = description.toLowerCase();

    // Food keywords
    if (
      desc.includes("restaurant") ||
      desc.includes("food") ||
      desc.includes("cafe") ||
      desc.includes("pizza") ||
      desc.includes("burger") ||
      desc.includes("swiggy") ||
      desc.includes("zomato") ||
      desc.includes("dominos")
    ) {
      return "Food";
    }

    // Transport keywords
    if (
      desc.includes("uber") ||
      desc.includes("ola") ||
      desc.includes("taxi") ||
      desc.includes("metro") ||
      desc.includes("bus") ||
      desc.includes("petrol") ||
      desc.includes("fuel") ||
      desc.includes("parking")
    ) {
      return "Transport";
    }

    // Entertainment keywords
    if (
      desc.includes("movie") ||
      desc.includes("cinema") ||
      desc.includes("netflix") ||
      desc.includes("spotify") ||
      desc.includes("game") ||
      desc.includes("entertainment")
    ) {
      return "Entertainment";
    }

    // Shopping keywords
    if (
      desc.includes("amazon") ||
      desc.includes("flipkart") ||
      desc.includes("mall") ||
      desc.includes("store") ||
      desc.includes("shopping") ||
      desc.includes("clothes")
    ) {
      return "Shopping";
    }

    // Bills keywords
    if (
      desc.includes("electricity") ||
      desc.includes("water") ||
      desc.includes("gas") ||
      desc.includes("internet") ||
      desc.includes("mobile") ||
      desc.includes("bill")
    ) {
      return "Bills";
    }

    // Healthcare keywords
    if (
      desc.includes("hospital") ||
      desc.includes("doctor") ||
      desc.includes("medical") ||
      desc.includes("pharmacy") ||
      desc.includes("medicine")
    ) {
      return "Healthcare";
    }

    return "Other";
  }

  checkDuplicate(transaction) {
    return this.data.expenses.some(
      (expense) =>
        expense.date === transaction.date &&
        Math.abs(expense.amount - Math.abs(transaction.amount)) < 0.01 &&
        expense.text
          .toLowerCase()
          .includes(transaction.description.toLowerCase().substring(0, 10))
    );
  }

  selectAllTransactions(select) {
    const checkboxes = document.querySelectorAll(".transaction-checkbox");
    const selectAllCheckbox = document.getElementById("selectAllCheckbox");

    checkboxes.forEach((checkbox) => {
      checkbox.checked = select;
      const row = checkbox.closest("tr");
      if (select) {
        row.classList.add("selected");
      } else {
        row.classList.remove("selected");
      }
    });

    if (selectAllCheckbox) {
      selectAllCheckbox.checked = select;
    }
  }

  updateSelectAllCheckbox() {
    const checkboxes = document.querySelectorAll(".transaction-checkbox");
    const selectAllCheckbox = document.getElementById("selectAllCheckbox");

    if (!selectAllCheckbox) return;

    const checkedCount = Array.from(checkboxes).filter(
      (cb) => cb.checked
    ).length;
    selectAllCheckbox.checked = checkedCount === checkboxes.length;
    selectAllCheckbox.indeterminate =
      checkedCount > 0 && checkedCount < checkboxes.length;
  }

  async importSelectedTransactions() {
    const checkboxes = document.querySelectorAll(
      ".transaction-checkbox:checked"
    );

    if (checkboxes.length === 0) {
      this.showMessage(
        "Please select at least one transaction to import",
        "warning"
      );
      return;
    }

    let importedCount = 0;
    let skippedCount = 0;

    checkboxes.forEach((checkbox) => {
      const index = parseInt(checkbox.dataset.index);
      const transaction = this.previewTransactions[index];

      if (!transaction || this.checkDuplicate(transaction)) {
        skippedCount++;
        return;
      }

      // Convert to expense format
      const expense = {
        id: Date.now() + Math.random(),
        text: transaction.description,
        amount: Math.abs(transaction.amount),
        category: transaction.category || "Other",
        date: transaction.date,
        timestamp: new Date().toISOString(),
        source: "credit-card-import",
      };

      this.data.expenses.push(expense);
      importedCount++;
    });

    // Record import history
    const importRecord = {
      id: Date.now(),
      fileName: this.selectedFile.name,
      date: new Date().toISOString(),
      transactionsFound: this.previewTransactions.length,
      transactionsImported: importedCount,
      transactionsSkipped: skippedCount,
      status:
        importedCount > 0
          ? skippedCount > 0
            ? "partial"
            : "success"
          : "failed",
    };

    this.data.creditImports.push(importRecord);
    this.saveData();
    this.updateAllData();

    // Show success message
    this.showMessage(
      `Import completed! ${importedCount} transactions imported, ${skippedCount} skipped.`,
      importedCount > 0 ? "success" : "warning"
    );

    // Reset UI
    this.resetImportUI();
  }

  resetImportUI() {
    const fileUploadArea = document.getElementById("fileUploadArea");
    const uploadOptions = document.getElementById("uploadOptions");
    const processingStatus = document.getElementById("processingStatus");
    const previewSection = document.getElementById("previewSection");
    const pdfFileInput = document.getElementById("pdfFileInput");

    // Reset file upload area
    fileUploadArea.innerHTML = `
      <div class="upload-icon">📄</div>
      <div class="upload-text">
        <p><strong>Click to upload</strong> or drag and drop</p>
        <p>PDF files only (Max 10MB)</p>
      </div>
    `;

    uploadOptions.style.display = "none";
    processingStatus.style.display = "none";
    previewSection.style.display = "none";

    pdfFileInput.value = "";
    document.getElementById("pdfPassword").value = "";
    document.getElementById("statementType").value = "auto";

    this.selectedFile = null;
    this.previewTransactions = [];
  }

  renderImportSummary() {
    const lastImportDate = document.getElementById("lastImportDate");
    const totalImported = document.getElementById("totalImported");
    const importSuccessRate = document.getElementById("importSuccessRate");

    if (!this.data.creditImports.length) {
      lastImportDate.textContent = "Never";
      totalImported.textContent = "0";
      importSuccessRate.textContent = "0%";
      return;
    }

    const lastImport =
      this.data.creditImports[this.data.creditImports.length - 1];
    const totalTransactions = this.data.creditImports.reduce(
      (sum, imp) => sum + imp.transactionsImported,
      0
    );
    const successfulImports = this.data.creditImports.filter(
      (imp) => imp.status === "success"
    ).length;
    const successRate = Math.round(
      (successfulImports / this.data.creditImports.length) * 100
    );

    lastImportDate.textContent = this.formatDate(lastImport.date);
    totalImported.textContent = totalTransactions.toString();
    importSuccessRate.textContent = `${successRate}%`;
  }

  renderImportHistory() {
    const importHistoryList = document.getElementById("importHistoryList");
    if (!importHistoryList) return;

    if (!this.data.creditImports.length) {
      importHistoryList.innerHTML = `
        <tr>
          <td colspan="6" style="text-align: center; color: var(--gray-500);">
            No import history available
          </td>
        </tr>
      `;
      return;
    }

    importHistoryList.innerHTML = this.data.creditImports
      .slice()
      .reverse()
      .map(
        (importRecord) => `
        <tr>
          <td>${this.formatDate(importRecord.date)}</td>
          <td>${importRecord.fileName}</td>
          <td>${importRecord.transactionsFound}</td>
          <td>${importRecord.transactionsImported}</td>
          <td><span class="status-badge ${importRecord.status}">${
          importRecord.status
        }</span></td>
          <td>
            <div class="action-buttons">
              <button class="delete-btn" onclick="financeManager.deleteImportRecord('${
                importRecord.id
              }')">
                Delete
              </button>
            </div>
          </td>
        </tr>
      `
      )
      .join("");
  }

  deleteImportRecord(id) {
    if (confirm("Are you sure you want to delete this import record?")) {
      this.data.creditImports = this.data.creditImports.filter(
        (record) => record.id != id
      );
      this.saveData();
      this.renderImportHistory();
      this.showMessage("Import record deleted successfully!", "success");
    }
  }
}

// PDF Processing Class
class PDFProcessor {
  constructor() {
    // Set PDF.js worker source
    if (typeof pdfjsLib !== "undefined") {
      pdfjsLib.GlobalWorkerOptions.workerSrc =
        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js";
    }
  }

  async extractTransactions(file, password = "", statementType = "auto") {
    try {
      if (typeof pdfjsLib === 'undefined') {
        throw new Error('PDF.js library not loaded. Please refresh the page and try again.');
      }

      const arrayBuffer = await file.arrayBuffer();
      const loadingTask = pdfjsLib.getDocument({
        data: arrayBuffer,
        password: password || undefined,
      });

      const pdf = await loadingTask.promise;
      let allText = "";

      // Extract text from all pages
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        const pageText = textContent.items.map((item) => item.str).join(" ");
        allText += pageText + "\n";
      }

      // Try to extract transactions using different methods
      let transactions = [];

      // Method 1: Try to find table-like structures
      transactions = this.extractFromTables(allText);

      // Method 2: If no tables found, try pattern matching
      if (transactions.length === 0) {
        transactions = this.extractFromPatterns(allText);
      }

      // Method 3: Bank-specific extraction
      if (transactions.length === 0 && statementType !== "auto") {
        transactions = this.extractBankSpecific(allText, statementType);
      }

      return this.cleanAndValidateTransactions(transactions);
    } catch (error) {
      console.error("PDF extraction error:", error);
      throw new Error(`Failed to process PDF: ${error.message}`);
    }
  }

  extractFromTables(text) {
    const transactions = [];
    const lines = text
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    // Look for table headers
    const headerPatterns = [
      /date.*transaction.*amount/i,
      /date.*description.*amount/i,
      /transaction date.*details.*amount/i,
    ];

    let headerIndex = -1;
    for (let i = 0; i < lines.length; i++) {
      if (headerPatterns.some((pattern) => pattern.test(lines[i]))) {
        headerIndex = i;
        break;
      }
    }

    if (headerIndex === -1) return transactions;

    // Process lines after header
    for (let i = headerIndex + 1; i < lines.length; i++) {
      const line = lines[i];
      const transaction = this.parseTransactionLine(line);
      if (transaction) {
        transactions.push(transaction);
      }
    }

    return transactions;
  }

  extractFromPatterns(text) {
    const transactions = [];
    const lines = text
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    // Look for lines that contain date, description, and amount patterns
    const transactionPattern =
      /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\s+(.+?)\s+([\d,]+\.?\d*)/;

    for (const line of lines) {
      const match = line.match(transactionPattern);
      if (match) {
        const [, dateStr, description, amountStr] = match;
        const transaction = {
          date: this.parseDate(dateStr),
          description: description.trim(),
          amount: this.parseAmount(amountStr),
        };

        if (transaction.date && transaction.amount > 0) {
          transactions.push(transaction);
        }
      }
    }

    return transactions;
  }

  extractBankSpecific(text, bankType) {
    switch (bankType.toLowerCase()) {
      case "hdfc":
        return this.extractHDFC(text);
      case "icici":
        return this.extractICICI(text);
      case "sbi":
        return this.extractSBI(text);
      case "axis":
        return this.extractAxis(text);
      default:
        return this.extractGeneric(text);
    }
  }

  extractHDFC(text) {
    const transactions = [];
    const lines = text.split("\n");

    // HDFC specific patterns
    const hdfcPattern =
      /(\d{2}\/\d{2}\/\d{4})\s+(\d{2}\/\d{2}\/\d{4})\s+(.+?)\s+([\d,]+\.\d{2})/;

    for (const line of lines) {
      const match = line.match(hdfcPattern);
      if (match) {
        const [, transDate, , description, amount] = match;
        transactions.push({
          date: this.parseDate(transDate),
          description: description.trim(),
          amount: this.parseAmount(amount),
        });
      }
    }

    return transactions;
  }

  extractICICI(text) {
    // ICICI specific extraction logic
    return this.extractGeneric(text);
  }

  extractSBI(text) {
    // SBI specific extraction logic
    return this.extractGeneric(text);
  }

  extractAxis(text) {
    // Axis specific extraction logic
    return this.extractGeneric(text);
  }

  extractGeneric(text) {
    // Generic extraction for unknown formats
    return this.extractFromPatterns(text);
  }

  parseTransactionLine(line) {
    // Try different line formats
    const formats = [
      // Format: DD/MM/YYYY Description Amount
      /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\s+(.+?)\s+([\d,]+\.?\d*)\s*$/,
      // Format: DD/MM/YYYY DD/MM/YYYY Description Amount
      /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\s+\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\s+(.+?)\s+([\d,]+\.?\d*)\s*$/,
      // Format with multiple spaces
      /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\s{2,}(.+?)\s{2,}([\d,]+\.?\d*)\s*$/,
    ];

    for (const format of formats) {
      const match = line.match(format);
      if (match) {
        const [, dateStr, description, amountStr] = match;
        const date = this.parseDate(dateStr);
        const amount = this.parseAmount(amountStr);

        if (date && amount > 0) {
          return {
            date: date,
            description: description.trim(),
            amount: amount,
          };
        }
      }
    }

    return null;
  }

  parseDate(dateStr) {
    // Handle different date formats
    const formats = [
      /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/, // DD/MM/YYYY or DD-MM-YYYY
      /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2})/, // DD/MM/YY or DD-MM-YY
    ];

    for (const format of formats) {
      const match = dateStr.match(format);
      if (match) {
        let [, day, month, year] = match;

        // Convert 2-digit year to 4-digit
        if (year.length === 2) {
          year = parseInt(year) > 50 ? `19${year}` : `20${year}`;
        }

        // Create date in YYYY-MM-DD format
        const date = new Date(
          parseInt(year),
          parseInt(month) - 1,
          parseInt(day)
        );
        if (!isNaN(date.getTime())) {
          return date.toISOString().split("T")[0];
        }
      }
    }

    return null;
  }

  parseAmount(amountStr) {
    // Remove commas and parse as float
    const cleaned = amountStr.replace(/,/g, "");
    const amount = parseFloat(cleaned);
    return isNaN(amount) ? 0 : amount;
  }

  cleanAndValidateTransactions(transactions) {
    return transactions
      .filter((t) => t.date && t.description && t.amount > 0)
      .map((t) => ({
        ...t,
        description: this.cleanDescription(t.description),
        amount: Math.round(t.amount * 100) / 100, // Round to 2 decimal places
      }))
      .sort((a, b) => new Date(a.date) - new Date(b.date));
  }

  cleanDescription(description) {
    // Clean up description text
    return description
      .replace(/\s+/g, " ")
      .replace(/[^\w\s\-\.]/g, "")
      .trim()
      .substring(0, 100); // Limit length
  }
}

// Initialize the application
let financeManager;

document.addEventListener("DOMContentLoaded", () => {
  financeManager = new FinanceManager();
});
