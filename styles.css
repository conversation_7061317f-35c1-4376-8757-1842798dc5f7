/* Modern Finance Manager - Comprehensive CSS */
:root {
  /* Color Palette */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --success-light: #34d399;
  --danger-color: #ef4444;
  --danger-light: #f87171;
  --warning-color: #f59e0b;
  --warning-light: #fbbf24;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Background Colors */
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --bg-card: #ffffff;
  --sidebar-bg: #1e293b;
  --sidebar-hover: #334155;

  /* Typography */
  --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Layout */
  --sidebar-width: 280px;
  --sidebar-collapsed: 80px;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  background: var(--bg-primary);
  color: var(--gray-800);
  line-height: 1.6;
  display: flex;
  min-height: 100vh;
}

/* Sidebar Navigation */
.sidebar {
  width: var(--sidebar-width);
  background: var(--sidebar-bg);
  color: var(--white);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  overflow-y: auto;
  transition: all var(--transition-normal);
  z-index: 1000;
  box-shadow: var(--shadow-lg);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed);
}

.sidebar.collapsed .nav-text {
  opacity: 0;
  visibility: hidden;
}

.sidebar.collapsed .sidebar-header h2 {
  opacity: 0;
  visibility: hidden;
}

.sidebar-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-600);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 700;
}

.sidebar-toggle {
  display: flex;
  background: none;
  border: none;
  color: var(--white);
  cursor: pointer;
  flex-direction: column;
  gap: 3px;
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
  background-color: var(--sidebar-hover);
}

.sidebar-toggle span {
  width: 20px;
  height: 2px;
  background: var(--white);
  transition: var(--transition-fast);
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-6);
  color: var(--gray-300);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
}

.nav-link:hover {
  background: var(--sidebar-hover);
  color: var(--white);
}

.nav-link.active {
  background: var(--sidebar-hover);
  color: var(--white);
  border-left-color: var(--primary-color);
}

.nav-icon {
  font-size: var(--font-size-lg);
  margin-right: var(--spacing-3);
  width: 24px;
  text-align: center;
}

.nav-text {
  font-weight: 500;
}

/* Main Content */
.main-content {
  margin-left: var(--sidebar-width);
  flex: 1;
  padding: var(--spacing-8);
  transition: margin-left var(--transition-normal);
  max-width: calc(100vw - var(--sidebar-width));
}

.sidebar.collapsed + .main-content {
  margin-left: var(--sidebar-collapsed);
  max-width: calc(100vw - var(--sidebar-collapsed));
}

/* Page System */
.page {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.page.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Page Headers */
.page-header {
  margin-bottom: var(--spacing-8);
}

.page-header h1 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin: 0 0 var(--spacing-2) 0;
  color: var(--gray-900);
}

.page-header p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  margin: 0;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.metric-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  transition: all var(--transition-normal);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metric-icon {
  font-size: var(--font-size-3xl);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: var(--gray-100);
}

.metric-card.balance .metric-icon {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--white);
}

.metric-card.income .metric-icon {
  background: linear-gradient(
    135deg,
    var(--success-color),
    var(--success-light)
  );
  color: var(--white);
}

.metric-card.expense .metric-icon {
  background: linear-gradient(135deg, var(--danger-color), var(--danger-light));
  color: var(--white);
}

.metric-card.investment .metric-icon {
  background: linear-gradient(
    135deg,
    var(--warning-color),
    var(--warning-light)
  );
  color: var(--white);
}

.metric-content h3 {
  margin: 0 0 var(--spacing-1) 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  margin: 0 0 var(--spacing-1) 0;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
}

.metric-change {
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.metric-change.positive {
  color: var(--success-color);
}

.metric-change.negative {
  color: var(--danger-color);
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.chart-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.chart-card h3 {
  margin: 0 0 var(--spacing-4) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
}

.chart-card canvas {
  max-height: 300px;
}

/* Summary Cards */
.income-summary,
.expense-summary,
.portfolio-summary,
.budget-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.summary-card,
.budget-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  text-align: center;
  transition: all var(--transition-normal);
}

.summary-card:hover,
.budget-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.summary-card h4,
.budget-card h4 {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.summary-card .amount,
.budget-card .amount {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
}

.expense-amount {
  color: var(--danger-color) !important;
}

/* Form Sections */
.form-section {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-8);
}

.form-section h3 {
  margin: 0 0 var(--spacing-6) 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-800);
}

.finance-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.form-control {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-control label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-700);
}

.form-control input,
.form-control select {
  padding: var(--spacing-3);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  transition: all var(--transition-fast);
}

.form-control input:focus,
.form-control select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control input[type="checkbox"] {
  width: auto;
  margin-right: var(--spacing-2);
}

/* Buttons */
.btn {
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  font-family: var(--font-family);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.btn.primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn.primary:hover {
  background: linear-gradient(
    135deg,
    var(--primary-dark),
    var(--primary-color)
  );
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Data Sections */
.data-section {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  margin-bottom: var(--spacing-8);
}

.section-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-800);
}

.filter-controls select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

/* Tables */
.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: var(--gray-50);
  padding: var(--spacing-4);
  text-align: left;
  font-weight: 600;
  color: var(--gray-700);
  border-bottom: 2px solid var(--gray-200);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-table td {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-100);
  font-size: var(--font-size-sm);
}

.data-table tbody tr {
  transition: all var(--transition-fast);
}

.data-table tbody tr:hover {
  background: var(--gray-50);
}

/* Recent Transactions */
.recent-transactions {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.recent-transactions h3 {
  margin: 0 0 var(--spacing-4) 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-800);
}

.transaction-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-color);
}

.transaction-item.expense {
  border-left-color: var(--danger-color);
}

.transaction-item.income {
  border-left-color: var(--success-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .sidebar-toggle {
    display: flex;
  }

  .main-content {
    margin-left: 0;
    max-width: 100vw;
    padding: var(--spacing-4);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-2);
}

.edit-btn,
.delete-btn {
  padding: var(--spacing-1) var(--spacing-3);
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: uppercase;
}

.edit-btn {
  background: var(--primary-color);
  color: var(--white);
}

.edit-btn:hover {
  background: var(--primary-dark);
}

.delete-btn {
  background: var(--danger-color);
  color: var(--white);
}

.delete-btn:hover {
  background: var(--danger-light);
}

/* Category Badges */
.category-badge {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Category Colors */
.category-food {
  background: #fef3c7;
  color: #92400e;
}
.category-transport {
  background: #dbeafe;
  color: #1e40af;
}
.category-entertainment {
  background: #fce7f3;
  color: #be185d;
}
.category-shopping {
  background: #ecfdf5;
  color: #065f46;
}
.category-bills {
  background: #fef2f2;
  color: #991b1b;
}
.category-healthcare {
  background: #f0f9ff;
  color: #0c4a6e;
}
.category-education {
  background: #f3e8ff;
  color: #6b21a8;
}
.category-travel {
  background: #f0fdf4;
  color: #166534;
}
.category-other {
  background: #f1f5f9;
  color: #475569;
}
.category-salary {
  background: #ecfdf5;
  color: #065f46;
}
.category-freelance {
  background: #fef3c7;
  color: #92400e;
}
.category-business {
  background: #dbeafe;
  color: #1e40af;
}
.category-investment {
  background: #f3e8ff;
  color: #6b21a8;
}
.category-rental {
  background: #f0fdf4;
  color: #166534;
}

/* Budget Categories */
.budget-categories {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.budget-categories h3 {
  margin: 0 0 var(--spacing-4) 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-800);
}

.budget-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
}

.budget-item:last-child {
  border-bottom: none;
}

.budget-progress {
  width: 200px;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.budget-progress-bar {
  height: 100%;
  background: var(--success-color);
  transition: width var(--transition-normal);
}

.budget-progress-bar.over-budget {
  background: var(--danger-color);
}

/* Loading States */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
}

.empty-state-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.empty-state-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
  margin-bottom: var(--spacing-2);
}

.empty-state-subtext {
  font-size: var(--font-size-sm);
  color: var(--gray-400);
}

/* Success/Error Messages */
.message {
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-4);
  font-weight: 600;
  text-align: center;
  box-shadow: var(--shadow-md);
}

.message.success {
  background: linear-gradient(
    135deg,
    var(--success-color),
    var(--success-light)
  );
  color: var(--white);
}

.message.error {
  background: linear-gradient(135deg, var(--danger-color), var(--danger-light));
  color: var(--white);
}

/* Credit Card Import Styles */
.import-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.upload-container {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
}

.file-upload-area {
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-lg);
  padding: var(--spacing-12);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--gray-50);
}

.file-upload-area:hover {
  border-color: var(--primary-color);
  background: var(--primary-color);
  background-opacity: 0.05;
}

.file-upload-area.dragover {
  border-color: var(--primary-color);
  background: rgba(37, 99, 235, 0.05);
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  color: var(--gray-400);
}

.upload-text p {
  margin: var(--spacing-2) 0;
  color: var(--gray-600);
}

.upload-text strong {
  color: var(--primary-color);
}

.upload-options {
  margin-top: var(--spacing-6);
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
}

.processing-status {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-8);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.progress-bar {
  width: 200px;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  transition: width var(--transition-normal);
  width: 0%;
}

.status-messages {
  max-height: 200px;
  overflow-y: auto;
}

.status-message {
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
}

.status-message.info {
  background: var(--gray-100);
  color: var(--gray-700);
}

.status-message.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-message.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.status-message.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.preview-section {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-8);
}

.preview-actions {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
}

.btn-loader {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}

.btn-loader::before {
  content: "";
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Transaction Preview Table */
.preview-transaction-row {
  transition: background-color var(--transition-fast);
}

.preview-transaction-row:hover {
  background-color: var(--gray-50);
}

.preview-transaction-row.selected {
  background-color: rgba(37, 99, 235, 0.05);
}

.category-select {
  padding: var(--spacing-2);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  background: var(--white);
}

.status-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.pending {
  background: var(--gray-100);
  color: var(--gray-600);
}

.status-badge.duplicate {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-badge.ready {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-badge.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* Responsive adjustments for collapsed sidebar */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    max-width: 100vw;
  }

  .sidebar-toggle {
    display: flex;
  }
}
